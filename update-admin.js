// update-admin.js - <PERSON>ript to update existing user to admin role
const mongoose = require('mongoose');
const User = require('./models/User');

async function updateToAdmin() {
  try {
    // Connect to MongoDB using the same connection string as server
    const mongoURI = process.env.MONGODB_URI || 'mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch';
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');

    // Get username from command line or use default
    const username = process.argv[2] || 'admin';

    // Find the user
    const user = await User.findOne({ username: username });
    if (!user) {
      console.log(`❌ User '${username}' not found`);
      console.log('Available users:');
      const allUsers = await User.find({}, 'username role accountStatus');
      allUsers.forEach(u => {
        console.log(`  - ${u.username} (role: ${u.role || 'user'}, status: ${u.accountStatus || 'active'})`);
      });
      process.exit(1);
    }

    console.log(`Found user: ${user.username}`);
    console.log(`Current role: ${user.role || 'user'}`);
    console.log(`Current status: ${user.accountStatus || 'active'}`);

    // Update user to admin role and ensure account is active
    user.role = 'admin';
    user.accountStatus = 'active';
    user.updatedAt = new Date();

    // Reset any login attempts/locks
    user.loginAttempts = 0;
    user.lockUntil = undefined;

    await user.save();

    console.log('✅ User updated successfully!');
    console.log('==========================================');
    console.log(`Username: ${user.username}`);
    console.log(`Role: ${user.role}`);
    console.log(`Status: ${user.accountStatus}`);
    console.log(`Email: ${user.email || 'Not set'}`);
    console.log('==========================================');
    console.log('You can now login to the admin dashboard at: /admin/login');

  } catch (error) {
    console.error('❌ Error updating user:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}

// Check command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node update-admin.js [username]');
  console.log('');
  console.log('Examples:');
  console.log('  node update-admin.js');
  console.log('  node update-admin.js admin');
  console.log('  node update-admin.js myuser');
  console.log('');
  console.log('Default username: admin');
  process.exit(0);
}

// Run the script
updateToAdmin();

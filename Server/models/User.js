// models/User.js
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  // MongoDB automatically creates _id field
  // We don't need to manually define it
  
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  email: {
    type: String,
    sparse: true, // Allow multiple null values but unique non-null values
    lowercase: true,
    trim: true
  },
  socialAuth: {
    google: {
      id: String,
      email: String,
      name: String,
      photo: String
    },
    apple: {
      id: String,
      email: String,
      name: String
    },
    facebook: {
      id: String,
      email: String,
      name: String,
      photo: String
    }
  },
  age: {
    type: Number,
    min: 18,
    max: 120
  },
  description: {
    type: String,
    maxlength: 500,
    default: ""
  },
  passions: {
    type: [String],
    default: []
  },
  images: {
    type: [String], // Array of image URLs or base64 encoded images
    default: [],
    validate: [arrayLimit, '{PATH} exceeds the limit of 4 images']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Add virtual getter for id that returns _id as string
userSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// Ensure virtual fields are included when converting to JSON
userSchema.set('toJSON', {
  virtuals: true,
  transform: (doc, ret) => {
    ret.id = ret._id.toString();
    return ret;
  }
});

// Validator to limit array length to 4
function arrayLimit(val) {
  return val.length <= 4;
}

module.exports = mongoose.model('User', userSchema);